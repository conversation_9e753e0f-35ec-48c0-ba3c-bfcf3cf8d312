import io
import logging
import datetime
import subprocess
import sys
from pathlib import Path

_logger_class_set_globally = False  # Track if setLoggerClass has been called


class Colors:
    """ANSI color codes for terminal output."""

    RESET = "\033[0m"
    BLUE = "\033[94m"
    ORANGE = "\033[38;5;208m"  # True orange (requires compatible terminal)
    # YELLOW = "\033[93m" # A fallback for orange if needed
    RED = "\033[91m"
    BOLD_RED = "\033[1;91m"  # For CRITICAL messages
    GREY = "\033[90m"


BRANCH_LEVEL_NUM = 25  # Between INFO (20) and WARNING (30)
logging.addLevelName(BRANCH_LEVEL_NUM, "BRANCH")


class ElaboratedFormatter(logging.Formatter):
    """
    Custom log formatter for elaborated logging output.

    Format: YYYY-MM-DD HH:MM:SS filename TYPE Message
    Colors: INFO (blue), WARNING (orange), ERROR (red), CRITIC<PERSON> (bold_red), BR<PERSON>CH (grey)
    Alignment: Ensures fields are aligned for readability.
    """

    LOG_FORMAT_TEMPLATE = "{date_str:<9} {time_str:<9} {filename_str:<16} [{level_colored_str}] {message_str}"
    FILENAME_WIDTH = 20
    LEVEL_NAME_WIDTH = 8  # Width for "CRITICAL"

    COLOR_MAP = {
        logging.DEBUG: Colors.GREY,  # Added DEBUG for completeness
        logging.INFO: Colors.BLUE,
        logging.WARNING: Colors.ORANGE,
        logging.ERROR: Colors.RED,
        logging.CRITICAL: Colors.BOLD_RED,
        BRANCH_LEVEL_NUM: Colors.GREY,
    }

    def __init__(self):
        super().__init__()

    def format(self, record):
        """Format a log record into the custom string format.

        Creates a formatted log message with aligned columns for date, time,
        filename, log level (with color), and the actual message. Handles
        exception and stack trace information if present.

        Args:
            record (logging.LogRecord): The log record to format containing
                timestamp, level, message, and optional exception information.

        Returns:
            str: The formatted log message string with ANSI color codes and
                proper column alignment.
        """
        date_str = datetime.datetime.fromtimestamp(record.created).strftime("%y-%m-%d")
        time_str = datetime.datetime.fromtimestamp(record.created).strftime("%H:%M:%S")

        pathname = record.pathname
        # Get filename without .py extension from the full path
        if pathname and not pathname.startswith("<"):  # Avoid issues with <stdin> etc.
            filename_str = Path(pathname).stem
        else:
            filename_str = record.module  # Fallback to module name

        # Truncate or pad filename for consistent width
        if len(filename_str) > self.FILENAME_WIDTH:
            # Truncate with ellipsis if too long
            filename_str = filename_str[: self.FILENAME_WIDTH - 3] + "..."
        else:
            filename_str = filename_str.ljust(self.FILENAME_WIDTH)

        level_name = record.levelname
        # Get the color for the current log level, default to RESET if not found
        level_color = self.COLOR_MAP.get(record.levelno, Colors.RESET)

        # Pad level name (e.g., "INFO    ") for alignment BEFORE applying color
        padded_level_name = level_name.ljust(self.LEVEL_NAME_WIDTH)
        level_colored_str = f"{level_color}{padded_level_name}{Colors.RESET}"

        message_str = record.getMessage()  # Get the formatted log message

        # Append exception information if present
        if record.exc_info and not record.exc_text:
            record.exc_text = self.formatException(record.exc_info)
        if record.exc_text:
            message_str = f"{message_str}\n{record.exc_text}"
        # Append stack information if present
        if record.stack_info:
            message_str = f"{message_str}\n{self.formatStack(record.stack_info)}"

        return self.LOG_FORMAT_TEMPLATE.format(
            date_str=date_str,
            time_str=time_str,
            filename_str=filename_str,
            level_colored_str=level_colored_str,
            message_str=message_str,
        )


class ElaboratedLogger(logging.Logger):
    """
    Custom logger class featuring an activation mechanism and specialized
    Git branch logging capabilities.
    """

    def __init__(self, name, level=logging.NOTSET):
        super().__init__(name, level)
        self._is_active = False
        # Attributes for capturing log history ---
        self._log_capture_handler = None
        self._log_capture_buffer = None

        # Add a NullHandler by default. It does nothing, but prevents warnings
        # if logging is attempted before activation.
        if (
            not self.hasHandlers()
        ):  # Check if handlers were already configured (e.g. by basicConfig)
            self.addHandler(logging.NullHandler())

    def activate(self, level=logging.INFO, stream=None):
        """Activate the logger for output.

        Sets the logger's effective level and adds a StreamHandler with the
        ElaboratedFormatter to output logs to the console. If called multiple
        times, it will update the existing handler or replace it to avoid
        duplicate outputs on the same stream.

        Args:
            level (int, optional): The minimum logging level this logger will process.
                Defaults to logging.INFO.
            stream (IO, optional): The stream to log to (e.g., sys.stdout, sys.stderr).
                Defaults to sys.stdout.
        """
        if stream is None:
            stream = sys.stdout  # Default to standard output

        self.setLevel(level)  # Set the logger's own threshold

        # Remove any NullHandler instances that might have been added by default
        self.handlers = [
            h for h in self.handlers if not isinstance(h, logging.NullHandler)
        ]

        # Manage StreamHandlers with ElaboratedFormatter to avoid duplication
        # or to update existing ones.
        existing_handler = None
        for h in self.handlers:
            if isinstance(h, logging.StreamHandler) and isinstance(
                h.formatter, ElaboratedFormatter
            ):
                existing_handler = h
                break

        if existing_handler:
            # Update existing handler
            existing_handler.setLevel(level)
            # Ensure formatter is our latest ElaboratedFormatter (in case it was changed)
            existing_handler.setFormatter(ElaboratedFormatter())
            if hasattr(existing_handler, "setStream") and callable(
                getattr(existing_handler, "setStream")
            ):  # For Python 3.7+
                existing_handler.setStream(stream)  # Update stream if possible
            elif (
                existing_handler.stream is not stream
            ):  # For older Python or if stream needs change
                self.removeHandler(existing_handler)  # Remove old, add new below
                existing_handler = None
                # Fall through to create new handler if stream changed and can't be updated

        if not existing_handler:  # No existing handler, or it was removed
            console_handler = logging.StreamHandler(stream)
            console_handler.setFormatter(ElaboratedFormatter())
            console_handler.setLevel(level)  # Handler also respects the level
            self.addHandler(console_handler)

        self._is_active = True
        # Optionally log activation (be careful of recursion if logger not fully set up)
        # self.info(f"Logger '{self.name}' activated. Level: {logging.getLevelName(level)}.")

    def start_capture(self, level=logging.INFO):
        """
        Starts capturing log messages to an in-memory string buffer.
        A separate, non-colored formatter is used for clean output.
        """
        if self._log_capture_handler is not None:
            self.warning("Log capturing is already active.")
            return

        self._log_capture_buffer = io.StringIO()
        self._log_capture_handler = logging.StreamHandler(self._log_capture_buffer)

        # Use a plain formatter for clean output without ANSI color codes
        plain_formatter = logging.Formatter(
            "%(asctime)s %(filename)-20s %(levelname)-8s %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        self._log_capture_handler.setFormatter(plain_formatter)
        self._log_capture_handler.setLevel(level)

        self.addHandler(self._log_capture_handler)
        self.info("Log capturing started.")

    @property
    def history(self) -> str:
        """
        Returns the captured log history as a single string.
        Returns an empty string if capturing is not active.
        """
        if self._log_capture_buffer:
            return self._log_capture_buffer.getvalue()
        return ""

    def stop_capture(self) -> str:
        """
        Stops capturing log messages, cleans up, and returns the full history.
        """
        if self._log_capture_handler is None:
            self.debug("stop_capture called but no capture was active.")
            return ""

        log_history = self.history

        self.removeHandler(self._log_capture_handler)
        self._log_capture_buffer.close()

        self._log_capture_handler = None
        self._log_capture_buffer = None

        self.info("Log capturing stopped.")
        return log_history

    def log_branch(self, message_suffix="", *args, **kws):
        """Log information about the current Git repository's branch.

        Logs information about the current Git repository's branch and its
        commit status relative to a primary branch ('master' or 'main').
        Uses the custom BRANCH log level.

        Args:
            message_suffix (str, optional): Additional text to append to the
                standardized branch log message. Defaults to "".
            *args: Additional arguments for the log message formatting.
            **kws: Additional keyword arguments for the underlying _log call.
        """
        # The logger's level and handler's level will determine if this is outputted.
        if not self.isEnabledFor(BRANCH_LEVEL_NUM):
            return  # Do nothing if this log level is not enabled

        # Check if we are in a Git repository
        try:
            subprocess.check_output(
                ["git", "rev-parse", "--is-inside-work-tree"],
                stderr=subprocess.DEVNULL,
                text=True,
                timeout=2,
            ).strip()
        except (
            subprocess.CalledProcessError,
            FileNotFoundError,
            subprocess.TimeoutExpired,
        ):
            msg = "Not a Git repository, Git command not found, or Git timed out."
            if message_suffix:
                msg += f" {message_suffix}"
            self._log(BRANCH_LEVEL_NUM, msg, args, **kws)
            return

        git_info_parts = []
        try:
            # Get current branch name
            current_branch = subprocess.check_output(
                ["git", "rev-parse", "--abbrev-ref", "HEAD"], text=True, timeout=2
            ).strip()
            git_info_parts.append(f"Branch: {current_branch}")

            # Determine primary branch to compare against (master or main)
            primary_branch_to_compare = None
            default_branches = ["master", "main"]

            for b_name in default_branches:
                try:
                    # Check if the local primary branch exists
                    subprocess.check_output(
                        ["git", "rev-parse", "--verify", f"refs/heads/{b_name}"],
                        stderr=subprocess.DEVNULL,
                        timeout=1,
                    )
                    primary_branch_to_compare = b_name
                    break  # Found one
                except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                    continue  # Try next default branch

            if primary_branch_to_compare:
                commits_behind_str = "N/A"
                try:
                    # Note: For truly accurate "commits behind remote master/main", a `git fetch` would be needed.
                    # This implementation compares against the local state of `primary_branch_to_compare`.
                    count_output = subprocess.check_output(
                        [
                            "git",
                            "rev-list",
                            "--count",
                            f"HEAD..{primary_branch_to_compare}",
                        ],
                        text=True,
                        stderr=subprocess.DEVNULL,  # Suppress errors like ambiguous ref names
                        timeout=2,
                    ).strip()
                    commits_behind = int(count_output)
                    commits_behind_str = str(commits_behind)
                except (
                    subprocess.CalledProcessError,
                    ValueError,
                    subprocess.TimeoutExpired,
                ):
                    # If current branch is the primary branch, count is 0.
                    if current_branch == primary_branch_to_compare:
                        commits_behind_str = "0"
                    # Otherwise, an error occurred or not applicable.
                git_info_parts.append(
                    f"Commits behind {primary_branch_to_compare}: {commits_behind_str}"
                )
            else:
                git_info_parts.append(
                    f"Default branches ({'/'.join(default_branches)}) not found locally for comparison."
                )

            final_message = ", ".join(git_info_parts)
            if message_suffix:  # Append any user-provided suffix
                final_message += f" {message_suffix}"
            self._log(BRANCH_LEVEL_NUM, final_message, args, **kws)

        except subprocess.CalledProcessError as e:
            err_msg = f"Error accessing Git information: {e.output.strip() if e.output else e.stderr.strip() if e.stderr else str(e)}"
            if message_suffix:
                err_msg += f" {message_suffix}"
            self._log(BRANCH_LEVEL_NUM, err_msg, args, **kws)
        except (FileNotFoundError, subprocess.TimeoutExpired) as e:
            # FileNotFoundError should be caught by the initial check. Timeout is for individual git commands.
            ex_msg = f"Git command issue: {str(e)}"
            if message_suffix:
                ex_msg += f" {message_suffix}"
            self._log(BRANCH_LEVEL_NUM, ex_msg, args, **kws)


def get_logger(name="app_logger"):
    """
    Retrieves or creates an ElaboratedLogger instance with the specified name.

    This function ensures that the Python logging system uses the
    ElaboratedLogger class for loggers obtained via `logging.getLogger()`,
    by calling `logging.setLoggerClass(ElaboratedLogger)` once.

    Args:
        name (str, optional): The name for the logger. Defaults to "app_logger".
                              Using `__name__` is a common practice for module-level loggers.

    Returns:
        ElaboratedLogger: The logger instance, typed as ElaboratedLogger.
    """
    global _logger_class_set_globally
    if not _logger_class_set_globally:
        logging.setLoggerClass(ElaboratedLogger)
        _logger_class_set_globally = True

    # Retrieve the logger instance. Thanks to setLoggerClass,
    # this will be an instance of ElaboratedLogger.
    logger = logging.getLogger(name)

    return logger
