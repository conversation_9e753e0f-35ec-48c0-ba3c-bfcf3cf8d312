"""
Stonky AI Agent for generating viral TikTok descriptions.

This module provides integration with the Ollama-based Stonky model
to automatically generate engaging TikTok descriptions for ReelStonks videos.
"""

import subprocess
import json
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from src.logger import get_logger

LOGGER = get_logger(__name__)


@dataclass
class InvestmentInfo:
    """Information about a single investment strategy."""
    name: str
    timing: str  # 'regular', 'lows', 'peaks'
    reinvest_dividends: bool


@dataclass
class GeneralInfo:
    """General information about the investment scenario."""
    investment_amount: int
    investment_kind: str  # 'monthly', 'weekly', etc.
    years_ago: int


class StonkyAgent:
    """AI agent for generating viral TikTok descriptions using Ollama."""
    
    def __init__(self, model_name: str = "stonky"):
        """Initialize the Stonky agent.
        
        Args:
            model_name: Name of the Ollama model to use.
        """
        self.model_name = model_name
        LOGGER.activate()
    
    def is_available(self) -> bool:
        """Check if Ollama and the Stonky model are available.
        
        Returns:
            True if the model is available, False otherwise.
        """
        try:
            result = subprocess.run(
                ['ollama', 'list'], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            if result.returncode == 0:
                return self.model_name in result.stdout
            return False
        except (FileNotFoundError, subprocess.TimeoutExpired):
            return False
    
    def create_prompt(self, general_info: GeneralInfo, investments: List[InvestmentInfo]) -> str:
        """Create a structured prompt for the Stonky model.
        
        Args:
            general_info: General investment information.
            investments: List of investment strategies.
            
        Returns:
            Formatted prompt string.
        """
        prompt = f"""General Information:
- investment_amount: ${general_info.investment_amount:,}
- investment_kind: {general_info.investment_kind}
- years_ago: {general_info.years_ago}

Specific Information:"""
        
        for i, investment in enumerate(investments, 1):
            prompt += f"""
Asset {i}:
- name: {investment.name}
- timing: {investment.timing}
- reinvest_dividends: {investment.reinvest_dividends}"""
        
        return prompt
    
    def generate_description(self, general_info: GeneralInfo, investments: List[InvestmentInfo]) -> Optional[str]:
        """Generate a viral TikTok description using the Stonky model.
        
        Args:
            general_info: General investment information.
            investments: List of investment strategies.
            
        Returns:
            Generated TikTok description or None if generation failed.
        """
        if not self.is_available():
            LOGGER.warning(f"Stonky model '{self.model_name}' is not available")
            return None
        
        prompt = self.create_prompt(general_info, investments)
        
        try:
            LOGGER.info("Generating TikTok description with Stonky AI...")
            result = subprocess.run(
                ['ollama', 'run', self.model_name, prompt],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                description = result.stdout.strip()
                LOGGER.info("✅ TikTok description generated successfully")
                return self._clean_description(description)
            else:
                LOGGER.error(f"Ollama error: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            LOGGER.error("Timeout while generating description")
            return None
        except Exception as e:
            LOGGER.error(f"Error generating description: {e}")
            return None
    
    def _clean_description(self, raw_description: str) -> str:
        """Clean and format the generated description.
        
        Args:
            raw_description: Raw output from the model.
            
        Returns:
            Cleaned description.
        """
        # Remove common model artifacts
        description = raw_description.replace('[/INST]', '').strip()
        
        # Extract just the description if there's additional text
        lines = description.split('\n')
        description_lines = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith('**') and not line.startswith('This description'):
                description_lines.append(line)
        
        return '\n'.join(description_lines[:10])  # Limit to reasonable length
    
    def generate_from_config(self, config: Dict[str, Any]) -> Optional[str]:
        """Generate description from ReelStonks configuration.
        
        Args:
            config: Configuration dictionary from ReelStonks.
            
        Returns:
            Generated TikTok description or None if generation failed.
        """
        try:
            # Extract general information
            general_info = GeneralInfo(
                investment_amount=config.get('investment_amount', 1000),
                investment_kind=config.get('investment_kind', 'monthly'),
                years_ago=config.get('years_ago', 10)
            )
            
            # Extract investment strategies
            investments = []
            strategies = config.get('strategies', [])
            
            for strategy in strategies:
                investment = InvestmentInfo(
                    name=strategy.get('display_name', strategy.get('ticker_symbol', 'Unknown')),
                    timing=strategy.get('timing', 'regular'),
                    reinvest_dividends=strategy.get('reinvest_dividends', False)
                )
                investments.append(investment)
            
            return self.generate_description(general_info, investments)
            
        except Exception as e:
            LOGGER.error(f"Error processing config: {e}")
            return None


def test_stonky_agent():
    """Test function for the Stonky agent."""
    agent = StonkyAgent()
    
    if not agent.is_available():
        print("❌ Stonky model not available")
        return
    
    # Test with sample data
    general_info = GeneralInfo(
        investment_amount=1000,
        investment_kind="monthly",
        years_ago=10
    )
    
    investments = [
        InvestmentInfo("Bitcoin", "regular", False),
        InvestmentInfo("Apple", "regular", True)
    ]
    
    description = agent.generate_description(general_info, investments)
    if description:
        print("✅ Generated description:")
        print(description)
    else:
        print("❌ Failed to generate description")


if __name__ == "__main__":
    test_stonky_agent()
