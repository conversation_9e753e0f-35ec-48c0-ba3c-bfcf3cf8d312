import os
from pathlib import Path
from typing import Optional
from tiktok_uploader.upload import upload_video
from src.logger import get_logger
from src.music import MusicManager

LOGGER = get_logger(__file__)
COOKIE_FILE = Path(__file__).parent.parent.parent.parent / "assets" / "cookies.txt"
DESCRIPTION = """
Stonks only go up, right? What do you think?

Music by <PERSON>
Check out: https://pixabay.com/users/**********************************/...

More content: github.com/your-username/reelstonks

#stocks #crypto #finance #investing #reelstonks #stockmarket #money
"""


def generate_description_single_line(music_creator: str | bool, music_link: str | bool) -> str:
    """
    Generates a TikTok description with proper hashtag formatting for API uploads.

    Args:
        music_creator: The name of the music creator.
        music_link: The link for the music.

    Returns:
        The formatted description string optimized for TikTok API hashtag recognition.
    """
    # Base content
    base_content = "Stonks only go up! What do you think?"

    # Music attribution
    music_section = ""
    if music_creator:
        music_section += f"\n\nMusic by {music_creator}"

    if music_link:
        music_section += f"\nCheck out: {music_link}"

    # Hashtags - Multiple formatting approaches for better API compatibility
    # Method 1: Each hashtag on its own line (often works better with API)
    hashtags_multiline = """

#stocks
#crypto
#finance
#investing
#reelstonks
#stockmarket
#money"""

    # Use multiline approach as it tends to work better with TikTok API
    return base_content + music_section + hashtags_multiline


def generate_description_alternative_hashtags(music_creator: str | bool, music_link: str | bool, hashtag_style: str = "multiline") -> str:
    """
    Alternative description generator with different hashtag formatting options.

    Args:
        music_creator: The name of the music creator.
        music_link: The link for the music.
        hashtag_style: Style of hashtags - "multiline", "spaced", "extra_spaced", or "minimal"

    Returns:
        Description with specified hashtag formatting.
    """
    base_content = "Stonks only go up! What do you think?"

    music_section = ""
    if music_creator:
        music_section += f"\n\nMusic by {music_creator}"
    if music_link:
        music_section += f"\nCheck out: {music_link}"

    # Different hashtag formatting approaches
    hashtag_options = {
        "multiline": """

#stocks
#crypto
#finance
#investing
#reelstonks
#stockmarket
#money""",

        "spaced": "\n\n#stocks #crypto #finance #investing #reelstonks #stockmarket #money",

        "extra_spaced": "\n\n#stocks  #crypto  #finance  #investing  #reelstonks  #stockmarket  #money",

        "minimal": "\n\n#stocks #finance #investing #reelstonks",

        "with_dots": "\n\n#stocks. #crypto. #finance. #investing. #reelstonks. #stockmarket. #money.",
    }

    hashtags = hashtag_options.get(hashtag_style, hashtag_options["multiline"])
    return base_content + music_section + hashtags


def filter_bmp_characters(text: str) -> str:
    """Filter out characters outside the Basic Multilingual Plane (BMP).

    ChromeDriver only supports characters in the BMP (Unicode code points 0-65535).
    This function removes any characters outside this range to prevent upload errors.

    Args:
        text (str): Input text that may contain non-BMP characters

    Returns:
        str: Text with non-BMP characters removed
    """
    return "".join(char for char in text if ord(char) <= 0xFFFF)


def titktok_upload_manager(
    video_path: str,
    music_filename: Optional[str] = None,
    cookies_file: str = COOKIE_FILE,
    headless: bool = True,
    privacy_type: str = "private",
    ) -> bool:
    """Uploads a video to TikTok using a cookies file for authentication.

    Creates a dynamic description based on the music used in the video by utilizing
    the existing MusicManager and creators.toml configuration.

    Args:
        video_path (str): The path to the video file.
        music_filename (Optional[str]): The music filename to get attribution for.
            If None, uses the first available music from MusicManager.
        cookies_file (str, optional): The path to the cookies file. Defaults to COOKIE_FILE.
        headless (bool, optional): Whether to run in headless mode. Defaults to True.
        privacy_type (str, optional): Privacy setting for the video. Defaults to "private".

    Returns:
        bool: True if the video was successfully uploaded, False otherwise.
    """

    LOGGER.activate()

    if not os.path.exists(cookies_file):
        LOGGER.error(f"❌ Cookie file not found! Please ensure '{cookies_file}' exists.")
        return False

    if not os.path.exists(video_path):
        LOGGER.error(f"❌ Video file not found! Please ensure '{video_path}' exists.")
        return False
    
    try:
        # Load music TOML data and generate description based on creator and link
        music_toml_data = None
        try:
            music_manager = MusicManager()
            if music_manager.has_music():
                selected_music = music_manager.select_music(music_filename)
                if selected_music:
                    # Load the full TOML config to get creator and link
                    import toml
                    config_file = music_manager.music_dir / "creators.toml"
                    if config_file.exists():
                        config_data = toml.load(config_file)
                        music_toml_data = config_data.get(selected_music.filename, {})
        except Exception as e:
            LOGGER.warning(f"Could not load music data: {e}")

        # Generate description
        if music_toml_data:
            LOGGER.info(f"Description generated from music TOML data")
            description = generate_description_single_line(
                music_toml_data.get("creator", False),
                music_toml_data.get("link", False),
            )
        else:
            # Fallback to static description
            LOGGER.info("Using fallback static description")
            description = DESCRIPTION

        # Filter description to remove non-BMP characters
        filtered_description = filter_bmp_characters(description)
        LOGGER.info(f"Using description: {filtered_description}")

        # Upload the video
        upload_video(
            filename=video_path,
            cookies=cookies_file,
            description=filtered_description,
            headless=headless,
            privacy_type=privacy_type,
        )
        LOGGER.info(f"✅ Video '{video_path}' uploaded successfully (as {privacy_type})!")
        return True
    except Exception as e:
        LOGGER.error(f"❌ An error occurred: {e}")
        return False


if __name__ == "__main__":
    titktok_upload_manager("assets/animations/wealth_animation.mp4", "dramatic-epic-background-305293.mp3")
