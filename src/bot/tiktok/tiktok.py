import os
from pathlib import Path
from tiktok_uploader.upload import upload_video
from src.logger import get_logger

LOGGER = get_logger(__file__)
COOKIE_FILE = Path(__file__).parent.parent.parent.parent / "assets" / "cookies.txt"


def tiktok_upload_handler(
    video_path: str,
    title: str,
    cookies_file: str = str(COOKIE_FILE),
    headless: bool = True,
    privacy_type: str = "private",
) -> list:
    """Uploads a video to TikTok and returns the result.

    Args:
        video_path (str): The path to the video file.
        title (str): The title of the video.
        cookies_file (str, optional): Path to the cookies file. Defaults to COOKIE_FILE.

    Returns:
        list: A list of video dictionaries that failed to upload. An empty list indicates success.
    """

    LOGGER.activate()

    if not os.path.exists(cookies_file):
        LOGGER.error(f"❌ Cookie file not found! Please ensure '{cookies_file}' exists.")
        return [{"path": video_path, "error": "Cookie file not found"}]

    if not os.path.exists(video_path):
        LOGGER.error(f"❌ Video file not found! Please ensure '{video_path}' exists.")
        return [{"path": video_path, "error": "Video file not found"}]

    try:
        # The upload_video function returns a list of failed videos.
        # An empty list means success.
        failed_videos = upload_video(
            filename=video_path,
            cookies=cookies_file,
            title=title,
            headless=headless,
            privacy_type=privacy_type,
        )

        if not failed_videos:
            # The video ID is part of the video object returned in the list
            # The video ID can be extracted to build the URL
            # Note: This assumes the library returns the video object with an ID on success,
            # which might not be guaranteed. Check library's documentation for specifics.
            # Assuming 'video_id' is a key in the returned dictionary
            # If not, this part needs adjustment based on actual return structure.
            # As of recent versions, the video ID is not returned, so manual construction is needed.
            # Return the list of failed videos for the caller to handle.
            LOGGER.info(f"✅ Video '{video_path}' uploaded successfully (as {privacy_type})!")
        else:
            LOGGER.error(f"❌ Video '{video_path}' failed to upload.")

        return failed_videos
    
    except Exception as e:
        LOGGER.error(f"❌ An error occurred during upload: {e}")
        return [{"path": video_path, "error": str(e)}]
