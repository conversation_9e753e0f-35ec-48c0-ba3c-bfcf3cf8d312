import os
from pathlib import Path
from typing import Optional
from tiktok_uploader.upload import upload_video
from src.logger import get_logger
from src.music import MusicManager

LOGGER = get_logger(__file__)
COOKIE_FILE = Path(__file__).parent.parent.parent.parent / "assets" / "cookies.txt"
DESCRIPTION = """
Stonks only go up, right? What do you think?

Music by <PERSON>
Check out: https://pixabay.com/users/**********************************/...

More content: github.com/your-username/reelstonks

#stocks #crypto #finance #investing #reelstonks #stockmarket #money
"""


def generate_description_single_line(music_creator: str | bool, music_link: str | bool) -> str:
    """
    Generates a single-line description, joining parts with a space.

    Args:
        music_creator: The name of the music creator.
        music_link: The link for the music.

    Returns:
        The formatted single-line description string.
    """
    parts = """Stonks only go up!

    #stocks #crypto #finance #investing #reelstonks #stockmarket #money"""

    # if music_creator:
    #     parts += f"""
    #     
    #     Music by {music_creator}."""

    # if music_link:
    #     parts += f"""
    #     
    #     Check out: {music_link}"""

    return parts


def filter_bmp_characters(text: str) -> str:
    """Filter out characters outside the Basic Multilingual Plane (BMP).

    ChromeDriver only supports characters in the BMP (Unicode code points 0-65535).
    This function removes any characters outside this range to prevent upload errors.

    Args:
        text (str): Input text that may contain non-BMP characters

    Returns:
        str: Text with non-BMP characters removed
    """
    return "".join(char for char in text if ord(char) <= 0xFFFF)


def titktok_upload_manager(
    video_path: str,
    music_filename: Optional[str] = None,
    cookies_file: str = COOKIE_FILE,
    headless: bool = True,
    privacy_type: str = "private",
    ) -> bool:
    """Uploads a video to TikTok using a cookies file for authentication.

    Creates a dynamic description based on the music used in the video by utilizing
    the existing MusicManager and creators.toml configuration.

    Args:
        video_path (str): The path to the video file.
        music_filename (Optional[str]): The music filename to get attribution for.
            If None, uses the first available music from MusicManager.
        cookies_file (str, optional): The path to the cookies file. Defaults to COOKIE_FILE.
        headless (bool, optional): Whether to run in headless mode. Defaults to True.
        privacy_type (str, optional): Privacy setting for the video. Defaults to "private".

    Returns:
        bool: True if the video was successfully uploaded, False otherwise.
    """

    LOGGER.activate()

    if not os.path.exists(cookies_file):
        LOGGER.error(f"❌ Cookie file not found! Please ensure '{cookies_file}' exists.")
        return False

    if not os.path.exists(video_path):
        LOGGER.error(f"❌ Video file not found! Please ensure '{video_path}' exists.")
        return False
    
    # Load music TOML data and generate description based on creator and link
    music_toml_data = MusicManager._load_creator_toml_file(music_filename)
    if music_toml_data:
        LOGGER.info(f"Description generated from music TOML data")
        description = generate_description_single_line(
            music_toml_data.get("creator", False),
            music_toml_data.get("link", False),
        )
    try:
        # Filter description to remove non-BMP characters
        filtered_description = filter_bmp_characters(DESCRIPTION)
        LOGGER.info(f"Using dynamic description: {filtered_description}")

        # Upload the video
        upload_video(
            filename=video_path,
            cookies=cookies_file,
            description=filtered_description,
            headless=headless,
            privacy_type=privacy_type,
        )
        LOGGER.info(f"✅ Video '{video_path}' uploaded successfully (as {privacy_type})!")
        return True
    except Exception as e:
        LOGGER.error(f"❌ An error occurred: {e}")
        return False


if __name__ == "__main__":
    titktok_upload_manager("assets/animations/wealth_animation.mp4", "dramatic-epic-background-305293.mp3")
