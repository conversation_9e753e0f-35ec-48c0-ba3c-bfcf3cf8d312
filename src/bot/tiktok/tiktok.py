import os
from pathlib import Path
from tiktok_uploader.upload import upload_video
from src.logger import get_logger

LOGGER = get_logger(__file__)
COOKIE_FILE = Path(__file__).parent.parent.parent.parent / "assets" / "cookies.txt"
DESCRIPTION = """
Stonks only go up, right? What do you think? 🤔 What if scenarios...
Music by <PERSON> from Pixabay - props to https://pixabay.com/music/!
"""
HASHTAGS = ["#stonks", "#finance", "#investing", "#reelstonks", "#memes", "#stocks", "#stockmarket", "#stockstagram", "#stocktwits", "#stockcharts", "#stockanalysis", "#stockadvice", "#stocktips", "#stocknews", "#stocktalk", "#stocktrading", "#stockinvesting", "#stockmarketanalysis", "#stockmarketnews", "#stockmarkettalk", "#stockmarkettrading", "#stockmarketinvest"]
DESCRIPTION = DESCRIPTION + " ".join(HASHTAGS)


def filter_bmp_characters(text: str) -> str:
    """Filter out characters outside the Basic Multilingual Plane (BMP).

    ChromeDriver only supports characters in the BMP (Unicode code points 0-65535).
    This function removes any characters outside this range to prevent upload errors.

    Args:
        text (str): Input text that may contain non-BMP characters

    Returns:
        str: Text with non-BMP characters removed
    """
    return "".join(char for char in text if ord(char) <= 0xFFFF)


def titktok_upload_manager(
    video_path: str, 
    cookies_file: str = COOKIE_FILE,
    headless: bool = True,
    privacy_type: str = "private",
    ) -> bool:
    """Uploads a video to TikTok using a cookies file for authentication.

    Args:
        video_path (str): The path to the video file.
        title (str): The title of the video.
        cookies_file (str, optional): The path to the cookies file. Defaults to "~/cookies.txt".

    Returns:
        bool: True if the video was successfully uploaded, False otherwise.
    """

    LOGGER.activate()

    if not os.path.exists(cookies_file):
        LOGGER.error(f"❌ Cookie file not found! Please ensure '{cookies_file}' exists.")
        return False

    if not os.path.exists(video_path):
        LOGGER.error(f"❌ Video file not found! Please ensure '{video_path}' exists.")
        return False

    try:
        # Filter description to remove non-BMP characters
        filtered_description = filter_bmp_characters(DESCRIPTION)
        LOGGER.info(f"Using filtered description: {filtered_description}")

        # Upload the video
        upload_video(
            filename=video_path,
            cookies=cookies_file,
            description=filtered_description,
            headless=headless,
            privacy_type=privacy_type,
        )
        LOGGER.info(f"✅ Video '{video_path}' uploaded successfully (as {privacy_type})!")
        return True
    except Exception as e:
        LOGGER.error(f"❌ An error occurred: {e}")
        return False
