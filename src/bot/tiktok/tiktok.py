import os
from pathlib import Path
from typing import Optional
from tiktok_uploader.upload import upload_video
from src.logger import get_logger
from src.music import MusicManager

LOGGER = get_logger(__file__)
COOKIE_FILE = Path(__file__).parent.parent.parent.parent / "assets" / "cookies.txt"
DESCRIPTION = """
Stonks only go up, right? What do you think?

 #stocks #crypto #finance #investing #reelstonks #stockmarket #money
"""


def generate_description_single_line(
    music_creator: str | bool,
    music_link: str | bool,
    hashtag_style: str = "traditional",
) -> str:
    """
    Alternative description generator with proven TikTok API hashtag approaches.

    Args:
        music_creator: The name of the music creator.
        music_link: The link for the music.
        hashtag_style: Style of hashtags.

    Returns:
        Description with specified hashtag formatting optimized for TikTok API.
    """
    base_content = "Stonks only go up! What do you think?"

    music_section = ""
    if music_creator:
        music_section += f"\n\nMusic by {music_creator}"
    if music_link:
        music_section += f"\nCheck out: {music_link}"

    # Proven hashtag approaches for TikTok API
    hashtag_options = {
        # Most reliable: traditional single line
        "traditional": "\n\n #fyp #stocks #finance #investing #reelstonks #stockmarket #viral #money",
        # Only at the very end with extra spacing
        "end_only": "\n\n\n#stocks #finance #investing #reelstonks",
        # Mixed with text
        "mixed": "\n\nTags: #stocks #finance #investing #reelstonks",
        # With separators
        "separated": "\n\n--- \n#stocks #finance #investing #reelstonks \n---",
        # Popular TikTok format
        "tiktok_style": "\n\n#fyp #stocks #finance #investing #viral #money",
    }

    hashtags = hashtag_options.get(hashtag_style, "traditional")
    return base_content + music_section + hashtags


def filter_bmp_characters(text: str) -> str:
    """Filter out characters outside the Basic Multilingual Plane (BMP).

    ChromeDriver only supports characters in the BMP (Unicode code points 0-65535).
    This function removes any characters outside this range to prevent upload errors.

    Args:
        text (str): Input text that may contain non-BMP characters

    Returns:
        str: Text with non-BMP characters removed
    """
    return "".join(char for char in text if ord(char) <= 0xFFFF)


def titktok_upload_manager(
    video_path: str,
    music_filename: Optional[str] = None,
    cookies_file: str = COOKIE_FILE,
    headless: bool = True,
    privacy_type: str = "private",
) -> bool:
    """Uploads a video to TikTok using a cookies file for authentication.

    Creates a dynamic description based on the music used in the video by utilizing
    the existing MusicManager and creators.toml configuration.

    Args:
        video_path (str): The path to the video file.
        music_filename (Optional[str]): The music filename to get attribution for.
            If None, uses the first available music from MusicManager.
        cookies_file (str, optional): The path to the cookies file. Defaults to COOKIE_FILE.
        headless (bool, optional): Whether to run in headless mode. Defaults to True.
        privacy_type (str, optional): Privacy setting for the video. Defaults to "private".

    Returns:
        bool: True if the video was successfully uploaded, False otherwise.
    """

    LOGGER.activate()

    if not os.path.exists(cookies_file):
        LOGGER.error(
            f"❌ Cookie file not found! Please ensure '{cookies_file}' exists."
        )
        return False

    if not os.path.exists(video_path):
        LOGGER.error(f"❌ Video file not found! Please ensure '{video_path}' exists.")
        return False

    try:
        # Load music TOML data and generate description based on creator and link
        music_toml_data = None
        try:
            music_manager = MusicManager()
            if music_manager.has_music():
                selected_music = music_manager.select_music(music_filename)
                if selected_music:
                    # Load the full TOML config to get creator and link
                    import toml

                    config_file = music_manager.music_dir / "creators.toml"
                    if config_file.exists():
                        config_data = toml.load(config_file)
                        music_toml_data = config_data.get(selected_music.filename, {})
        except Exception as e:
            LOGGER.warning(f"Could not load music data: {e}")

        # Generate description with different hashtag approaches
        if music_toml_data:
            if not music_toml_data.get("creator", False) and not music_toml_data.get(
                "link", False
            ):
                LOGGER.warning(
                    "No creator or link found in music TOML data, using fallback static description"
                )
                description = DESCRIPTION
            else:
                LOGGER.info(f"Description generated from music TOML data")
                # Try different hashtag styles - change this to test different approaches
                description = generate_description_single_line(
                    music_toml_data.get("creator", False),
                    music_toml_data.get("link", False),
                    hashtag_style="traditional",  # Change this to: "minimal", "no_hashtags", "tiktok_style", etc.
                )
        else:
            # Fallback to static description
            LOGGER.info("Using fallback static description")
            description = DESCRIPTION

        # Filter description to remove non-BMP characters
        filtered_description = filter_bmp_characters(description)
        LOGGER.info(f"Using description: {filtered_description}")

        # Upload the video
        upload_video(
            filename=video_path,
            cookies=cookies_file,
            description=filtered_description,
            headless=headless,
            privacy_type=privacy_type,
        )
        LOGGER.info(
            f"✅ Video '{video_path}' uploaded successfully (as {privacy_type})!"
        )
        return True
    except Exception as e:
        LOGGER.error(f"❌ An error occurred: {e}")
        return False
