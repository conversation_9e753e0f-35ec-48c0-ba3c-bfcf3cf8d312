"""
LLM module for ReelStonks.

This module provides functionality for interacting with Large Language Models (LLMs)
to generate captions and titles for animations.
"""

import ollama


modelfile_content = f"""
FROM {base_model}
SYSTEM "You are a senior C++ software engineer. Your answers are expert, concise, and contain code examples. Do not explain that you are an AI."
PARAMETER temperature 0.5
PARAMETER stop "```"
"""

class LLM:

    def _fine_tune(self, prompt: str, response: str) -> None:
        """Fine-tune the model with a prompt and response."""
        ollama.fine_tune(prompt, response)

    def __init__(self, model: str = "llama3:8b"):
        self.model = model
        ollama.create(model=self.model, modelfile=MODELFILE_CONTENT)

    def generate_title(self, prompt: str) -> str:
        """Generate a title for an animation based on a prompt."""
        response = ollama.generate(prompt)
        return response["response"]
