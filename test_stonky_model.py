#!/usr/bin/env python3
"""
Test script for the Stonky AI model to generate TikTok descriptions.
This script demonstrates how to use the improved Modelfile.
"""

def create_test_prompt():
    """Create a test prompt for the Stonky model."""
    
    # Example 1: Comparison scenario
    comparison_prompt = """
General Information:
- investment_amount: $1000
- investment_kind: monthly
- years_ago: 10

Specific Information:
Asset 1:
- name: Bitcoin
- timing: regular
- reinvest_dividends: false

Asset 2:
- name: Apple
- timing: regular
- reinvest_dividends: true
"""

    # Example 2: Single asset with perfect timing
    single_asset_prompt = """
General Information:
- investment_amount: $500
- investment_kind: monthly
- years_ago: 5

Specific Information:
Asset 1:
- name: NVIDIA
- timing: lows
- reinvest_dividends: true
"""

    # Example 3: Timing comparison
    timing_comparison_prompt = """
General Information:
- investment_amount: $1000
- investment_kind: monthly
- years_ago: 8

Specific Information:
Asset 1:
- name: S&P 500
- timing: lows
- reinvest_dividends: true

Asset 2:
- name: S&P 500
- timing: peaks
- reinvest_dividends: true
"""

    return {
        "comparison": comparison_prompt,
        "single_asset": single_asset_prompt,
        "timing_comparison": timing_comparison_prompt
    }

def test_ollama_integration():
    """Test if ollama is available and the stonky model exists."""
    import subprocess
    
    try:
        # Check if ollama is installed
        result = subprocess.run(['ollama', 'list'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama is installed")
            if 'stonky' in result.stdout:
                print("✅ Stonky model is available")
                return True
            else:
                print("❌ Stonky model not found. Run: ollama create stonky -f assets/Modelfile")
                return False
        else:
            print("❌ Ollama not found or not running")
            return False
    except FileNotFoundError:
        print("❌ Ollama not installed")
        return False

if __name__ == "__main__":
    print("🤖 Testing Stonky AI Model for TikTok Descriptions")
    print("=" * 50)
    
    # Test ollama integration
    if test_ollama_integration():
        print("\n📝 Example prompts for testing:")
        prompts = create_test_prompt()
        
        for scenario, prompt in prompts.items():
            print(f"\n--- {scenario.upper()} SCENARIO ---")
            print(prompt.strip())
            print("\n💡 To test this prompt, run:")
            print(f'ollama run stonky "{prompt.strip()}"')
    
    print("\n🚀 To rebuild the model with improvements, run:")
    print("ollama create stonky -f assets/Modelfile")
