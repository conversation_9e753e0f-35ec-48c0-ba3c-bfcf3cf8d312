"""
Main entry point for ReelStonks application.

Usage:
    python main.py

    or

    uv run main.py

Requires:
    - Python 3.13+
    - FFmpeg (for video generation)
    - Configuration in config.toml (see config.toml.template for details)

Generates:
    - Timestamped directory in assets/animations/
    - Wealth projection animation in the timestamped directory
"""

import tomllib
import pandas as pd
import time
from config import ReelStonksManager
from src.bot import TelegramManager
from src.bot.telegram.interactive_menu import InteractiveMenu
from src.logger import get_logger
from src.data.yahoo_fetcher import get_stock_price_timeseries
from src.animation.time_series_animation import create_wealth_animation
from src.utils.title_constructor import construct_string_title
from src.utils.setup import setup_project
from src.utils.paths import get_config_path, get_timestamped_animations_dir
from src.utils.config_merger import merge_strategy_config, validate_merged_config
from src.bot.tiktok.tiktok import titktok_upload_manager

LOGGER = get_logger(__file__)
LOGGER.activate()


if __name__ == "__main__":
    # Record start time for duration calculation
    start_time = time.time()

    # Set up project directories first
    LOGGER.info("🚀 Initializing ReelStonks...")
    if not setup_project(verbose=True):
        LOGGER.error("❌ Failed to set up project directories. Exiting.")
        exit(1)

    # Load configuration from TOML
    LOGGER.info("📋 Loading master configuration file...")
    config_path = get_config_path()
    with open(config_path, "rb") as f:
        config_data = tomllib.load(f)

    # Initialize Config with values from the TOML file (temporary for Telegram setup)
    telegram_config = ReelStonksManager._load_telegram_config(config_data)
    telegram_manager = TelegramManager(telegram_config)

    # Interactive menu system (if enabled and Telegram is configured)
    if telegram_manager.interactive_menu:
        LOGGER.info("🎛️ Starting interactive menu system...")
        interactive_menu = InteractiveMenu(telegram_manager)

        # Show menu and wait for user selection
        selected_config = interactive_menu.show_menu_and_wait(config_data)

        if selected_config:
            # Merge selected configuration with base config
            LOGGER.info("🔄 Merging configuration with user selection")
            config_data = merge_strategy_config(config_data, selected_config)

            # Validate merged configuration
            if not validate_merged_config(config_data):
                LOGGER.error("❌ Invalid merged configuration, falling back to default")
                # Reload original config
                with open(config_path, "rb") as f:
                    config_data = tomllib.load(f)
        else:
            LOGGER.info("🔧 Using default configuration from config.toml")
    else:
        if telegram_manager.telegram_enabled:
            LOGGER.info("🔧 Interactive menu disabled, using default configuration")
        else:
            LOGGER.info("🔧 Telegram not enabled, using default configuration")

    # Initialize the final Config with the (potentially merged) configuration
    LOGGER.info("🔧 Initializing ReelStonksManager with final configuration...")
    config = ReelStonksManager(config_dict=config_data)

    projections = []
    stock_data_cache = {}  # Cache to avoid re-fetching same ticker data

    # Loop through strategies
    for strategy in config.strategies:
        LOGGER.info(
            f"Processing strategy '{strategy.strategy_id}' for {strategy.ticker_symbol} ..."
        )
        # Check if we already have data for this ticker
        if strategy.ticker_symbol not in stock_data_cache:
            # Fetch stock data for ticker
            stock_data = get_stock_price_timeseries(
                strategy.ticker_symbol,
                interval=config.interval,
                start_date=config.start_date_str,
                end_date=config.today_str,
                save_data=config.save_data,
            )
            stock_data_cache[strategy.ticker_symbol] = stock_data
        else:
            stock_data = stock_data_cache[strategy.ticker_symbol]
            LOGGER.info(f" --- Using cached data for {strategy.ticker_symbol}")

        # Get corresponding investment object for this strategy
        if strategy.strategy_id in config.failed_strategies:
            LOGGER.error(
                f"❌ Skipping strategy '{strategy.strategy_id}' due to unsupported investment object."
            )
            telegram_manager.send_message(f"Failed ticker: {strategy.ticker_symbol}...")
            continue

        # Send strategy start notification to Telegram
        telegram_manager.send_message(f"Processed ticker: {strategy.ticker_symbol}...")

        # Initialize investment object
        investment_object = config.investment_objects[strategy.strategy_id]
        investment = investment_object(
            df=stock_data,
            investment_amount=config.investment_amount,
            tax_rate=config.tax_rate,
            tax_free_return_threshold_per_annu=config.tax_free_return_threshold_per_annu,
        )

        # Get wealth projection and append to list as series
        projection = investment.invest(
            strategy.reinvest_dividends, config.data_frequency_timestamp
        )
        projection_series = projection["Wealth"].rename(strategy.display_name)
        projections.append(projection_series)

    LOGGER.info("💰 Concatenating projections and filtering for investment periods...")

    total_investments = projection["Invested Cash"].cumsum().rename("Total Investments")
    projections.append(total_investments)
    projections = pd.concat(projections, axis=1)

    telegram_manager.send_message(
        "Investment projection completed. Starting animation..."
    )

    # Create title using strategy display names
    strategy_names = [strategy.display_name for strategy in config.strategies]
    title = construct_string_title(
        strategy_names,
        config.investment_amount,
        config.years_ago,
        config.interval_title,
    )

    # Create animation with dynamic path
    animations_dir = get_timestamped_animations_dir()
    animation_filename = animations_dir / "wealth_animation.mp4"
    create_wealth_animation(
        projections,
        config.years_ago,
        filename=str(animation_filename),
        duration_sec=config.animation_duration_seconds,
        fps=config.fps,
        title=title,
        music_filename=config.use_music,
    )
    telegram_manager.send_message("Animation completed. Sending video...")
    telegram_manager.send_video(animation_filename, caption=title)

    # Calculate total duration and send completion notification
    total_duration = time.time() - start_time

    # Send completion notification
    LOGGER.info(f"✅ ReelStonks completed successfully in {total_duration:.2f} seconds")

    telegram_manager.send_message(
        f"✅ ReelStonks completed successfully in {total_duration:.2f} seconds. Send full log report..."
    )

    # Upload video to TikTok
    if config.upload_cfg.get("tiktok", False):
        LOGGER.info(" Uploading video to TikTok...")
        titktok_upload_manager(str(animation_filename))
        LOGGER.info("✅ Video uploaded to TikTok!")
        telegram_manager.send_message("✅ Video uploaded to TikTok!")
        
